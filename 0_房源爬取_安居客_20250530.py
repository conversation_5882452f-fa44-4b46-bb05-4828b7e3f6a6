import requests
from bs4 import BeautifulSoup
import pandas as pd
import time
import random
from fake_useragent import UserAgent
import json
import re


class HousingScraper:
    def __init__(self):
        self.ua = UserAgent()
        # 使用安居客天津租房页面
        self.base_url = "https://tj.zu.anjuke.com/fangyuan/"
        self.session = requests.Session()
        # 模拟正常浏览器
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.132 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Referer': 'https://tj.zu.anjuke.com/'
        }
        self.data = []

    def handle_antibot_redirect(self, html):
        """处理反爬虫重定向页面"""
        try:
            # 查找重定向URL
            if "@@xxzlGatewayUrl" in html:
                soup = BeautifulSoup(html, 'html.parser')
                redirect_div = soup.find('div', {'id': '@@xxzlGatewayUrl'})
                if redirect_div:
                    redirect_url = redirect_div.text.strip()
                    print(f"发现重定向URL: {redirect_url}")
                    # 尝试访问重定向URL
                    response = self.session.get(redirect_url, headers=self.headers, timeout=15)
                    return response.text
        except Exception as e:
            print(f"处理重定向失败: {e}")
        return None

    def get_page(self, page_num=1):
        """获取指定页码的房源列表页"""
        if page_num == 1:
            url = self.base_url
        else:
            url = f"{self.base_url}p{page_num}/"
        print(f"🌐 正在访问: {url}")

        # 随机更换User-Agent
        user_agents = [
            'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.132 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/88.0.4324.150 Safari/537.36'
        ]
        self.headers['User-Agent'] = random.choice(user_agents)

        # 添加更多延迟和重试机制
        max_retries = 3
        for attempt in range(max_retries):
            try:
                # 每次请求前随机延迟
                delay = random.uniform(3, 8)
                print(f"⏰ 等待 {delay:.1f} 秒...")
                time.sleep(delay)

                # 随机访问首页建立session
                if random.random() < 0.3:  # 30%概率访问首页
                    print("🔄 随机访问首页建立session...")
                    try:
                        self.session.get("https://tj.zu.anjuke.com/", headers=self.headers, timeout=10)
                        time.sleep(random.uniform(2, 4))
                    except:
                        pass

                # 添加随机的referer
                if page_num > 1:
                    self.headers['Referer'] = f"https://tj.zu.anjuke.com/fangyuan/p{page_num-1}/"

                response = self.session.get(url, headers=self.headers, timeout=25, allow_redirects=True)
                response.raise_for_status()
                print(f"📊 响应状态码: {response.status_code}")
                print(f"📄 响应内容长度: {len(response.text)}")
                print(f"🔗 最终URL: {response.url}")

                # 检查是否被重定向到验证页面
                if "antibot" in response.text or "verifycode" in response.text or "callback.58.com" in response.text:
                    print(f"🚫 第 {attempt + 1} 次尝试检测到反爬虫验证页面")

                    # 尝试处理重定向
                    redirect_result = self.handle_antibot_redirect(response.text)
                    if redirect_result and len(redirect_result) > 1000:
                        print("✅ 成功通过重定向获取页面")
                        return redirect_result

                    if attempt < max_retries - 1:
                        wait_time = random.uniform(20, 40)
                        print(f"⏳ 等待 {wait_time:.1f} 秒后重试...")
                        time.sleep(wait_time)

                        # 重新建立session
                        print("🔄 重新建立session...")
                        self.session = requests.Session()
                        continue
                    else:
                        print("❌ 多次尝试后仍遇到验证页面")
                        return None

                # 检查页面是否正常
                if len(response.text) < 10000:
                    print(f"⚠️ 页面内容过短，可能有问题")
                    if attempt < max_retries - 1:
                        continue

                print("✅ 页面获取成功")
                return response.text

            except Exception as e:
                print(f"❌ 第 {attempt + 1} 次尝试获取页面失败: {e}")
                if attempt < max_retries - 1:
                    wait_time = random.uniform(8, 15)
                    print(f"⏳ 等待 {wait_time:.1f} 秒后重试...")
                    time.sleep(wait_time)
                    continue
                else:
                    print("💥 所有尝试都失败了")
                    return None

    def parse_list_page(self, html):
        """解析列表页，提取房源信息"""
        if not html:
            return []

        soup = BeautifulSoup(html, 'html.parser')

        # 调试：打印HTML片段查看结构
        print("HTML长度:", len(html))
        print("页面标题:", soup.title.text if soup.title else "无标题")

        # 尝试多种可能的选择器（移动端）
        house_items = soup.select('.list-item')
        if not house_items:
            house_items = soup.select('.item')
        if not house_items:
            house_items = soup.select('.house-item')
        if not house_items:
            house_items = soup.select('.rent-item')
        if not house_items:
            house_items = soup.select('[data-item-id]')
        if not house_items:
            house_items = soup.select('.zu-itemmod')
        if not house_items:
            house_items = soup.select('.item-mod')

        print(f"找到房源条目数量: {len(house_items)}")

        # 如果还是找不到，打印部分HTML内容用于调试
        if not house_items:
            print("未找到房源条目，打印前1000字符的HTML:")
            print(html[:1000])
            return []

        houses = []
        for i, item in enumerate(house_items):
            try:
                print(f"正在解析第 {i+1} 个房源条目...")

                # 提取房源标题和链接 - 尝试多种选择器
                title_elem = item.select_one('.house-title a')
                if not title_elem:
                    title_elem = item.select_one('a[title]')
                if not title_elem:
                    title_elem = item.select_one('.title a')
                if not title_elem:
                    title_elem = item.select_one('h3 a')

                if not title_elem:
                    print(f"第 {i+1} 个条目未找到标题元素")
                    continue

                title = title_elem.text.strip()
                link = title_elem.get('href', '')
                if link and not link.startswith('http'):
                    link = 'https://tj.zu.anjuke.com' + link

                # 提取房源基本信息
                house_info = ""
                info_elem = item.select_one('.details-item')
                if not info_elem:
                    info_elem = item.select_one('.zu-info')
                if not info_elem:
                    info_elem = item.select_one('.house-info')
                if info_elem:
                    house_info = info_elem.text.strip()

                # 提取位置信息
                location = ""
                loc_elem = item.select_one('.comm-address')
                if not loc_elem:
                    loc_elem = item.select_one('.address')
                if not loc_elem:
                    loc_elem = item.select_one('.location')
                if loc_elem:
                    location = loc_elem.text.strip()

                # 提取价格
                price = ""
                price_elem = item.select_one('.price strong')
                if not price_elem:
                    price_elem = item.select_one('.price')
                if not price_elem:
                    price_elem = item.select_one('.money')
                if price_elem:
                    price = price_elem.text.strip()

                price_unit = ""
                unit_elem = item.select_one('.price .unit')
                if unit_elem:
                    price_unit = unit_elem.text.strip()

                full_price = f"{price}{price_unit}" if price else "价格未知"

                # 提取标签
                tags = []
                tag_elems = item.select('.tags-bottom span')
                if not tag_elems:
                    tag_elems = item.select('.tags span')
                if not tag_elems:
                    tag_elems = item.select('.tag')
                for tag in tag_elems:
                    tags.append(tag.text.strip())

                house_data = {
                    'title': title,
                    'link': link,
                    'house_info': house_info,
                    'location': location,
                    'price': full_price,
                    'tags': ', '.join(tags)
                }

                print(f"成功解析: {title[:30]}...")
                houses.append(house_data)

            except Exception as e:
                print(f"解析第 {i+1} 个房源信息失败: {e}")
                # 打印该条目的HTML用于调试
                print(f"条目HTML片段: {str(item)[:200]}...")
                continue

        return houses

    def scrape(self, max_pages=5):
        """爬取指定页数的房源信息"""
        failed_pages = []

        for page in range(1, max_pages + 1):
            print(f"\n{'='*50}")
            print(f"正在爬取第 {page} 页...")
            print(f"{'='*50}")

            success = False
            max_retries = 5

            for attempt in range(max_retries):
                if attempt > 0:
                    print(f"第 {page} 页第 {attempt + 1} 次尝试...")

                html = self.get_page(page)
                houses = self.parse_list_page(html)

                if houses:
                    self.data.extend(houses)
                    print(f"✅ 第 {page} 页成功爬取 {len(houses)} 条房源信息")
                    print(f"📊 累计已爬取 {len(self.data)} 条房源信息")
                    success = True
                    break
                else:
                    print(f"❌ 第 {page} 页第 {attempt + 1} 次尝试失败")
                    if attempt < max_retries - 1:
                        # 逐渐增加延迟时间
                        delay = random.uniform(10 + attempt * 5, 20 + attempt * 10)
                        print(f"⏰ 等待 {delay:.1f} 秒后重试...")
                        time.sleep(delay)

                        # 重新建立session
                        if attempt >= 2:
                            print("🔄 重新建立session...")
                            self.session = requests.Session()
                            self.session.get("https://tj.zu.anjuke.com/", headers=self.headers, timeout=10)
                            time.sleep(3)

            if not success:
                print(f"💥 第 {page} 页多次尝试后仍然失败，记录到失败列表")
                failed_pages.append(page)

            # 页面间的延迟
            if page < max_pages:
                delay = random.uniform(8, 15)
                print(f"⏳ 页面间等待 {delay:.1f} 秒...")
                time.sleep(delay)

        # 重试失败的页面
        if failed_pages:
            print(f"\n🔄 开始重试失败的页面: {failed_pages}")
            for page in failed_pages:
                print(f"\n重试第 {page} 页...")
                # 更长的延迟
                time.sleep(random.uniform(30, 60))

                html = self.get_page(page)
                houses = self.parse_list_page(html)

                if houses:
                    self.data.extend(houses)
                    print(f"✅ 重试成功！第 {page} 页爬取 {len(houses)} 条房源信息")
                else:
                    print(f"❌ 第 {page} 页重试仍然失败")

        print(f"\n🎉 爬取完成！总共获取 {len(self.data)} 条房源信息")
        return self.data

    def save_to_csv(self, filename="tianjin_konggang_houses.csv"):
        """将爬取的数据保存为CSV文件"""
        if not self.data:
            print("没有数据可保存")
            return

        df = pd.DataFrame(self.data)
        df.to_csv(filename, index=False, encoding='utf-8-sig')
        print(f"数据已保存到 {filename}")

    def save_to_excel(self, filename="tianjin_konggang_houses.xlsx"):
        """将爬取的数据保存为Excel文件"""
        if not self.data:
            print("没有数据可保存")
            return

        df = pd.DataFrame(self.data)
        df.to_excel(filename, index=False)
        print(f"数据已保存到 {filename}")


if __name__ == "__main__":
    print("🏠 天津房源爬虫启动...")
    print("=" * 60)

    scraper = HousingScraper()

    # 爬取更多页数据
    houses = scraper.scrape(max_pages=10)  # 爬取10页数据

    print("\n" + "=" * 60)
    print(f"🎯 爬取完成！共获取到 {len(houses)} 条房源信息")
    print("=" * 60)

    if houses:
        print("\n💾 正在保存数据...")
        scraper.save_to_csv()
        scraper.save_to_excel()
        print("✅ 数据保存完成！")

        # 显示一些统计信息
        print(f"\n📈 数据统计:")
        print(f"   - 总房源数量: {len(houses)}")

        # 统计价格分布
        prices = []
        for house in houses:
            price_str = house.get('price', '')
            if price_str and price_str != '价格未知':
                # 提取数字
                import re
                price_match = re.search(r'(\d+)', price_str)
                if price_match:
                    prices.append(int(price_match.group(1)))

        if prices:
            print(f"   - 平均租金: {sum(prices)/len(prices):.0f} 元/月")
            print(f"   - 最低租金: {min(prices)} 元/月")
            print(f"   - 最高租金: {max(prices)} 元/月")
    else:
        print("❌ 没有获取到任何数据")

    print("\n🎉 程序执行完毕！")